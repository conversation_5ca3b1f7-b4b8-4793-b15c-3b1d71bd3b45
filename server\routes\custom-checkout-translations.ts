import { Router, Request, Response } from 'express';
import { z } from 'zod';
import { isAdmin } from '../middleware/auth';
import { storage } from '../storage';

const translationsRouter = Router();

// Schema for creating/updating translations
const translationSchema = z.object({
  languageCode: z.string().min(2, 'Language code is required (e.g., fr, es, de)'),
  title: z.string().optional(),
  productName: z.string().optional(),
  productDescription: z.string().optional(),
  confirmationMessage: z.string().optional(),
  headerTitle: z.string().optional(),
  footerText: z.string().optional(),
});

// Get all translations for a checkout page (admin only)
translationsRouter.get('/:checkoutPageId', isAdmin, async (req: Request, res: Response) => {
  try {
    const checkoutPageId = parseInt(req.params.checkoutPageId);
    
    if (isNaN(checkoutPageId)) {
      return res.status(400).json({ message: 'Invalid checkout page ID' });
    }

    // Check if checkout page exists
    const checkoutPage = await storage.getCustomCheckoutPage(checkoutPageId);
    if (!checkoutPage) {
      return res.status(404).json({ message: 'Checkout page not found' });
    }

    const translations = await storage.getCustomCheckoutPageTranslations(checkoutPageId);
    res.json(translations);
  } catch (error) {
    console.error('Error fetching translations:', error);
    res.status(500).json({ message: 'Failed to fetch translations' });
  }
});

// Get a specific translation (admin only)
translationsRouter.get('/:checkoutPageId/:languageCode', isAdmin, async (req: Request, res: Response) => {
  try {
    const checkoutPageId = parseInt(req.params.checkoutPageId);
    const { languageCode } = req.params;
    
    if (isNaN(checkoutPageId)) {
      return res.status(400).json({ message: 'Invalid checkout page ID' });
    }

    const translation = await storage.getCustomCheckoutPageTranslation(checkoutPageId, languageCode);
    
    if (!translation) {
      return res.status(404).json({ message: 'Translation not found' });
    }

    res.json(translation);
  } catch (error) {
    console.error('Error fetching translation:', error);
    res.status(500).json({ message: 'Failed to fetch translation' });
  }
});

// Create or update a translation (admin only)
translationsRouter.put('/:checkoutPageId/:languageCode', isAdmin, async (req: Request, res: Response) => {
  try {
    const checkoutPageId = parseInt(req.params.checkoutPageId);
    const { languageCode } = req.params;
    
    if (isNaN(checkoutPageId)) {
      return res.status(400).json({ message: 'Invalid checkout page ID' });
    }

    // Check if checkout page exists
    const checkoutPage = await storage.getCustomCheckoutPage(checkoutPageId);
    if (!checkoutPage) {
      return res.status(404).json({ message: 'Checkout page not found' });
    }

    const validatedData = translationSchema.parse(req.body);

    // Check if translation already exists
    const existingTranslation = await storage.getCustomCheckoutPageTranslation(checkoutPageId, languageCode);
    
    let translation;
    if (existingTranslation) {
      // Update existing translation
      translation = await storage.updateCustomCheckoutPageTranslation(checkoutPageId, languageCode, {
        ...validatedData,
        updatedAt: new Date().toISOString()
      });
    } else {
      // Create new translation
      translation = await storage.createCustomCheckoutPageTranslation({
        checkoutPageId,
        languageCode,
        ...validatedData,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      });
    }

    res.json(translation);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        message: 'Validation error',
        errors: error.errors
      });
    }
    console.error('Error creating/updating translation:', error);
    res.status(500).json({ message: 'Failed to create/update translation' });
  }
});

// Delete a translation (admin only)
translationsRouter.delete('/:checkoutPageId/:languageCode', isAdmin, async (req: Request, res: Response) => {
  try {
    const checkoutPageId = parseInt(req.params.checkoutPageId);
    const { languageCode } = req.params;
    
    if (isNaN(checkoutPageId)) {
      return res.status(400).json({ message: 'Invalid checkout page ID' });
    }

    const success = await storage.deleteCustomCheckoutPageTranslation(checkoutPageId, languageCode);
    
    if (!success) {
      return res.status(404).json({ message: 'Translation not found' });
    }

    res.json({ message: 'Translation deleted successfully' });
  } catch (error) {
    console.error('Error deleting translation:', error);
    res.status(500).json({ message: 'Failed to delete translation' });
  }
});

export { translationsRouter };
