import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Trash2, Globe, RefreshCw, CheckCircle, AlertCircle } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { apiRequest } from '@/lib/api';

interface Translation {
  language: string;
  createdAt: string;
  updatedAt: string;
}

interface TranslationManagerProps {
  checkoutPageId: number;
  onTranslationChange?: () => void;
}

const SUPPORTED_LANGUAGES = [
  { code: 'fr', name: 'French', flag: '🇫🇷' },
  { code: 'es', name: 'Spanish', flag: '🇪🇸' },
  { code: 'pt', name: 'Portuguese', flag: '🇵🇹' },
  { code: 'de', name: 'German', flag: '🇩🇪' },
  { code: 'nl', name: 'Dutch', flag: '🇳🇱' },
  { code: 'pl', name: 'Polish', flag: '🇵🇱' },
  { code: 'it', name: 'Italian', flag: '🇮🇹' },
  { code: 'sv', name: 'Swedish', flag: '🇸🇪' },
  { code: 'fi', name: 'Finnish', flag: '🇫🇮' },
  { code: 'da', name: 'Danish', flag: '🇩🇰' },
  { code: 'ro', name: 'Romanian', flag: '🇷🇴' },
  { code: 'sq', name: 'Albanian', flag: '🇦🇱' },
  { code: 'sk', name: 'Slovak', flag: '🇸🇰' },
  { code: 'no', name: 'Norwegian', flag: '🇳🇴' },
];

export function TranslationManager({ checkoutPageId, onTranslationChange }: TranslationManagerProps) {
  const [translations, setTranslations] = useState<Translation[]>([]);
  const [selectedLanguage, setSelectedLanguage] = useState<string>('');
  const [isTranslating, setIsTranslating] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();

  const loadTranslations = async () => {
    try {
      const response = await apiRequest(`/api/custom-checkout/${checkoutPageId}/translations`);
      if (response.ok) {
        const data = await response.json();
        setTranslations(data.translations || []);
      }
    } catch (error) {
      console.error('Failed to load translations:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadTranslations();
  }, [checkoutPageId]);

  const handleTranslate = async () => {
    if (!selectedLanguage) {
      toast({
        title: "Error",
        description: "Please select a language to translate to",
        variant: "destructive",
      });
      return;
    }

    setIsTranslating(true);
    try {
      const response = await apiRequest(`/api/custom-checkout/${checkoutPageId}/translate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ language: selectedLanguage }),
      });

      if (response.ok) {
        const data = await response.json();
        toast({
          title: "Translation Complete",
          description: `Successfully translated to ${getLanguageName(selectedLanguage)}`,
        });
        
        // Reload translations
        await loadTranslations();
        setSelectedLanguage('');
        onTranslationChange?.();
      } else {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Translation failed');
      }
    } catch (error) {
      toast({
        title: "Translation Failed",
        description: error.message || "Failed to translate page",
        variant: "destructive",
      });
    } finally {
      setIsTranslating(false);
    }
  };

  const handleDeleteTranslation = async (language: string) => {
    try {
      const response = await apiRequest(`/api/custom-checkout/${checkoutPageId}/translations/${language}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        toast({
          title: "Translation Deleted",
          description: `Deleted ${getLanguageName(language)} translation`,
        });
        
        // Reload translations
        await loadTranslations();
        onTranslationChange?.();
      } else {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete translation');
      }
    } catch (error) {
      toast({
        title: "Delete Failed",
        description: error.message || "Failed to delete translation",
        variant: "destructive",
      });
    }
  };

  const getLanguageName = (code: string) => {
    const lang = SUPPORTED_LANGUAGES.find(l => l.code === code);
    return lang ? `${lang.flag} ${lang.name}` : code;
  };

  const getAvailableLanguages = () => {
    const existingLanguages = translations.map(t => t.language);
    return SUPPORTED_LANGUAGES.filter(lang => !existingLanguages.includes(lang.code));
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Globe className="h-5 w-5" />
            Translation Manager
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <RefreshCw className="h-6 w-6 animate-spin" />
            <span className="ml-2">Loading translations...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Globe className="h-5 w-5" />
          Translation Manager
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Create New Translation */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Create New Translation</h3>
          <div className="flex gap-3">
            <Select value={selectedLanguage} onValueChange={setSelectedLanguage}>
              <SelectTrigger className="flex-1">
                <SelectValue placeholder="Select language to translate to" />
              </SelectTrigger>
              <SelectContent>
                {getAvailableLanguages().map((lang) => (
                  <SelectItem key={lang.code} value={lang.code}>
                    {lang.flag} {lang.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Button 
              onClick={handleTranslate} 
              disabled={!selectedLanguage || isTranslating}
              className="min-w-[120px]"
            >
              {isTranslating ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Translating...
                </>
              ) : (
                <>
                  <Globe className="h-4 w-4 mr-2" />
                  Translate
                </>
              )}
            </Button>
          </div>
          {getAvailableLanguages().length === 0 && (
            <p className="text-sm text-muted-foreground">
              All supported languages have been translated.
            </p>
          )}
        </div>

        {/* Existing Translations */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Existing Translations</h3>
          {translations.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <AlertCircle className="h-8 w-8 mx-auto mb-2" />
              <p>No translations created yet.</p>
              <p className="text-sm">Create your first translation above.</p>
            </div>
          ) : (
            <div className="space-y-3">
              {translations.map((translation) => (
                <div
                  key={translation.language}
                  className="flex items-center justify-between p-4 border rounded-lg"
                >
                  <div className="flex items-center gap-3">
                    <Badge variant="secondary" className="text-sm">
                      {getLanguageName(translation.language)}
                    </Badge>
                    <div className="text-sm text-muted-foreground">
                      Created: {new Date(translation.createdAt).toLocaleDateString()}
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDeleteTranslation(translation.language)}
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Usage Instructions */}
        <div className="bg-blue-50 p-4 rounded-lg">
          <h4 className="font-medium text-blue-900 mb-2">How to Use Translations</h4>
          <div className="text-sm text-blue-800 space-y-1">
            <p>• Translations are cached and only use the API when you click "Translate"</p>
            <p>• Users can access translated pages by adding <code>?lang=XX</code> to the URL</p>
            <p>• Example: <code>/checkout/your-page?lang=fr</code> for French</p>
            <p>• Delete and recreate translations to update them with new content</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
