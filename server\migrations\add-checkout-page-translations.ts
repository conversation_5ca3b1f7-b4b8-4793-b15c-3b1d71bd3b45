import { db } from '../db.js';
import { sql } from 'drizzle-orm';

export async function addCheckoutPageTranslations() {
  console.log('Creating checkout_page_translations table...');

  try {
    // Create checkout_page_translations table
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS checkout_page_translations (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        checkout_page_id INTEGER NOT NULL,
        language TEXT NOT NULL,
        title TEXT,
        product_name TEXT,
        product_description TEXT,
        confirmation_message TEXT,
        header_title TEXT,
        footer_text TEXT,
        ui_translations TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        UNIQUE(checkout_page_id, language)
      )
    `);

    console.log('Successfully created checkout_page_translations table');
  } catch (error) {
    console.error('Error creating checkout_page_translations table:', error);
    // Don't throw error if table already exists
    if (!error.message?.includes('already exists')) {
      throw error;
    }
  }
}
