import { useMemo } from 'react';

export interface CheckoutUITranslations {
  // Header and navigation
  secureCheckout: string;
  
  // Product section
  trialSubscription: string;
  
  // Form labels and headings
  customerInformation: string;
  fullName: string;
  emailAddress: string;
  country: string;
  chooseApplication: string;
  macAddress: string;
  
  // Placeholders
  fullNamePlaceholder: string;
  emailPlaceholder: string;
  selectCountryPlaceholder: string;
  chooseApplicationPlaceholder: string;
  macAddressPlaceholder: string;
  
  // Notes and warnings
  subscriberOnlyNote: string;
  macAddressNote: string;
  
  // Pricing section
  subtotal: string;
  total: string;
  
  // Buttons and actions
  completePurchase: string;
  tryAgain: string;
  completePayment: string;
  
  // Status messages
  loadingCheckout: string;
  processingOrder: string;
  orderSuccessful: string;
  somethingWentWrong: string;
  checkoutExpired: string;
  
  // Payment messages
  paypalInvoiceMessage: string;
  paymentLinkMessage: string;
  paymentFormMessage: string;
  paymentButtonMessage: string;
  emailSentMessage: string;
  
  // Error messages
  errorMessage: string;
  expiredMessage: string;
  
  // Subscription extension
  subscriptionExtension: string;
  subscriptionExtensionNote: string;
  
  // Footer
  allRightsReserved: string;
  securePaymentProcessing: string;
}

// Default English translations
const defaultTranslations: CheckoutUITranslations = {
  // Header and navigation
  secureCheckout: 'Secure Checkout',
  
  // Product section
  trialSubscription: 'Trial Subscription',
  
  // Form labels and headings
  customerInformation: 'Customer Information',
  fullName: 'Full Name',
  emailAddress: 'Email Address',
  country: 'Country',
  chooseApplication: 'Choose the application you are using:',
  macAddress: 'Your Mac Address (required)',
  
  // Placeholders
  fullNamePlaceholder: 'John Smith',
  emailPlaceholder: 'Your Email',
  selectCountryPlaceholder: 'Select your country',
  chooseApplicationPlaceholder: 'Choose your application',
  macAddressPlaceholder: '00:1A:72:c9:dc:a4',
  
  // Notes and warnings
  subscriberOnlyNote: 'This purchase is only available to existing subscribers. Please enter the email address associated with your subscription. New users should order a test plan first. If you want to buy a second subscription just add the email you have signed with in the first time.',
  macAddressNote: 'Please enter the MAC address of your device, EX: 00:1A:72:c9:dc:a4.',
  
  // Pricing section
  subtotal: 'Subtotal',
  total: 'Total',
  
  // Buttons and actions
  completePurchase: 'Complete Purchase',
  tryAgain: 'Try Again',
  completePayment: 'Complete Payment',
  
  // Status messages
  loadingCheckout: 'Loading Checkout Page',
  processingOrder: 'Processing Your Order',
  orderSuccessful: 'Order Successful!',
  somethingWentWrong: 'Something went wrong',
  checkoutExpired: 'Checkout Page Expired',
  
  // Payment messages
  paypalInvoiceMessage: "You'll receive a PayPal invoice via email to complete your purchase securely.",
  paymentLinkMessage: "You'll receive a payment link via email to complete your purchase securely.",
  paymentFormMessage: 'Please complete your payment using the secure payment form below.',
  paymentButtonMessage: 'Please complete your payment using the button below.',
  emailSentMessage: "We've sent a payment link to your email address. Please check your inbox to complete your purchase.",
  
  // Error messages
  errorMessage: "We couldn't process your order. Please try again or contact support if the problem persists.",
  expiredMessage: 'This checkout page has expired and is no longer available.',
  
  // Subscription extension
  subscriptionExtension: 'Subscription Extension',
  subscriptionExtensionNote: 'This checkout is for existing subscribers only. If you\'re a new user, please order a test plan first before extending your subscription.',
  
  // Footer
  allRightsReserved: 'All rights reserved.',
  securePaymentProcessing: 'Secure payment processing provided by PayPal.'
};

export function useCheckoutTranslations(uiTranslations?: CheckoutUITranslations | null): CheckoutUITranslations {
  return useMemo(() => {
    // If we have translated UI text from the API, use it; otherwise use defaults
    return uiTranslations || defaultTranslations;
  }, [uiTranslations]);
}
