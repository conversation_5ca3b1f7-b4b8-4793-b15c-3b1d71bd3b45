// Simple script to create the translation table
import Database from 'better-sqlite3';

async function createTranslationTable() {
  try {
    console.log('Creating translation table...');
    
    // Open the database
    const db = new Database('data.db');
    
    // Create the translation table
    const createTableSQL = `
      CREATE TABLE IF NOT EXISTS checkout_page_translations (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        checkout_page_id INTEGER NOT NULL,
        language TEXT NOT NULL,
        title TEXT,
        product_name TEXT,
        product_description TEXT,
        confirmation_message TEXT,
        header_title TEXT,
        footer_text TEXT,
        ui_translations TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        UNIQUE(checkout_page_id, language)
      )
    `;
    
    db.exec(createTableSQL);
    
    console.log('✅ Translation table created successfully!');
    
    // Check if table exists
    const tables = db.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name='checkout_page_translations'").all();
    console.log('Tables found:', tables);
    
    db.close();
    console.log('Database connection closed.');
    
  } catch (error) {
    console.error('❌ Error creating translation table:', error);
  }
}

createTranslationTable();
