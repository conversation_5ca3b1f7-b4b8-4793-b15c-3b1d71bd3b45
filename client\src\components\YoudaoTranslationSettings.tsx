import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Loader2, Globe, TestTube, CheckCircle, XCircle } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface YoudaoTranslationSettingsProps {
  checkoutPageId: number;
  initialEnabled?: boolean;
  onEnabledChange?: (enabled: boolean) => void;
}

interface SupportedLanguage {
  code: string;
  name: string;
}

interface TranslationTestResult {
  success: boolean;
  originalText: string;
  translatedText: string;
  fromLang: string;
  toLang: string;
  error?: string;
}

export function YoudaoTranslationSettings({ 
  checkoutPageId, 
  initialEnabled = false,
  onEnabledChange 
}: YoudaoTranslationSettingsProps) {
  const [enabled, setEnabled] = useState(initialEnabled);
  const [loading, setLoading] = useState(false);
  const [testLoading, setTestLoading] = useState(false);
  const [supportedLanguages, setSupportedLanguages] = useState<SupportedLanguage[]>([]);
  const [testLanguage, setTestLanguage] = useState('fr');
  const [testResult, setTestResult] = useState<TranslationTestResult | null>(null);
  const { toast } = useToast();

  // Load supported languages on component mount
  useEffect(() => {
    loadSupportedLanguages();
  }, []);

  const loadSupportedLanguages = async () => {
    try {
      const response = await fetch('/api/youdao-translation/languages');
      if (response.ok) {
        const languages = await response.json();
        setSupportedLanguages(languages);
      }
    } catch (error) {
      console.error('Error loading supported languages:', error);
    }
  };

  const handleToggleEnabled = async (newEnabled: boolean) => {
    setLoading(true);
    try {
      const response = await fetch(`/api/youdao-translation/checkout-page/${checkoutPageId}/toggle`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ enabled: newEnabled }),
      });

      if (response.ok) {
        const result = await response.json();
        setEnabled(result.youdaoTranslationEnabled);
        onEnabledChange?.(result.youdaoTranslationEnabled);
        
        toast({
          title: 'Translation Settings Updated',
          description: `Youdao translation has been ${result.youdaoTranslationEnabled ? 'enabled' : 'disabled'} for this checkout page.`,
        });
      } else {
        throw new Error('Failed to update translation settings');
      }
    } catch (error) {
      console.error('Error updating translation settings:', error);
      toast({
        title: 'Error',
        description: 'Failed to update translation settings. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleTestTranslation = async () => {
    if (!testLanguage) return;

    setTestLoading(true);
    setTestResult(null);

    try {
      const testText = 'Welcome to our checkout page. Please complete your purchase below.';
      
      const response = await fetch('/api/youdao-translation/test', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          text: testText,
          fromLang: 'en',
          toLang: testLanguage,
        }),
      });

      if (response.ok) {
        const result = await response.json();
        setTestResult({
          success: true,
          originalText: result.originalText,
          translatedText: result.translatedText,
          fromLang: result.fromLang,
          toLang: result.toLang,
        });
        
        toast({
          title: 'Translation Test Successful',
          description: `Successfully translated text to ${supportedLanguages.find(l => l.code === testLanguage)?.name || testLanguage}.`,
        });
      } else {
        const error = await response.json();
        setTestResult({
          success: false,
          originalText: testText,
          translatedText: '',
          fromLang: 'en',
          toLang: testLanguage,
          error: error.error || 'Translation failed',
        });
        
        toast({
          title: 'Translation Test Failed',
          description: error.error || 'Failed to translate text. Please check your API configuration.',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error testing translation:', error);
      setTestResult({
        success: false,
        originalText: 'Test text',
        translatedText: '',
        fromLang: 'en',
        toLang: testLanguage,
        error: 'Network error occurred',
      });
      
      toast({
        title: 'Translation Test Failed',
        description: 'Network error occurred. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setTestLoading(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Globe className="h-5 w-5" />
          Youdao Translation Settings
        </CardTitle>
        <CardDescription>
          Enable automatic translation for this checkout page using Youdao API. 
          Customers can access translated versions by adding ?lang=fr (or other language codes) to the URL.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Enable/Disable Toggle */}
        <div className="flex items-center justify-between">
          <div className="space-y-0.5">
            <Label htmlFor="youdao-enabled" className="text-base">
              Enable Youdao Translation
            </Label>
            <div className="text-sm text-muted-foreground">
              Allow automatic translation of checkout page content
            </div>
          </div>
          <Switch
            id="youdao-enabled"
            checked={enabled}
            onCheckedChange={handleToggleEnabled}
            disabled={loading}
          />
        </div>

        {enabled && (
          <>
            {/* Supported Languages */}
            <div className="space-y-3">
              <Label className="text-base">Supported Languages</Label>
              <div className="flex flex-wrap gap-2">
                {supportedLanguages.map((language) => (
                  <Badge key={language.code} variant="secondary">
                    {language.name} ({language.code})
                  </Badge>
                ))}
              </div>
              <div className="text-sm text-muted-foreground">
                Customers can access translations by adding ?lang=CODE to the URL (e.g., ?lang=fr for French)
              </div>
            </div>

            {/* URL Examples */}
            <div className="space-y-3">
              <Label className="text-base">URL Examples</Label>
              <div className="bg-muted p-3 rounded-md space-y-1 text-sm font-mono">
                <div>English (default): /checkout/your-page</div>
                <div>French: /checkout/your-page?lang=fr</div>
                <div>Spanish: /checkout/your-page?lang=es</div>
                <div>German: /checkout/your-page?lang=de</div>
              </div>
            </div>

            {/* Translation Test */}
            <div className="space-y-3">
              <Label className="text-base">Test Translation</Label>
              <div className="flex gap-2">
                <Select value={testLanguage} onValueChange={setTestLanguage}>
                  <SelectTrigger className="w-48">
                    <SelectValue placeholder="Select language" />
                  </SelectTrigger>
                  <SelectContent>
                    {supportedLanguages
                      .filter(lang => lang.code !== 'en')
                      .map((language) => (
                        <SelectItem key={language.code} value={language.code}>
                          {language.name}
                        </SelectItem>
                      ))}
                  </SelectContent>
                </Select>
                <Button 
                  onClick={handleTestTranslation} 
                  disabled={testLoading || !testLanguage}
                  variant="outline"
                >
                  {testLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Testing...
                    </>
                  ) : (
                    <>
                      <TestTube className="mr-2 h-4 w-4" />
                      Test Translation
                    </>
                  )}
                </Button>
              </div>

              {/* Test Result */}
              {testResult && (
                <div className="mt-4 p-4 border rounded-md">
                  <div className="flex items-center gap-2 mb-3">
                    {testResult.success ? (
                      <CheckCircle className="h-5 w-5 text-green-500" />
                    ) : (
                      <XCircle className="h-5 w-5 text-red-500" />
                    )}
                    <span className="font-medium">
                      Translation Test {testResult.success ? 'Successful' : 'Failed'}
                    </span>
                  </div>
                  
                  <div className="space-y-2 text-sm">
                    <div>
                      <span className="font-medium">Original (English):</span>
                      <div className="bg-muted p-2 rounded mt-1">{testResult.originalText}</div>
                    </div>
                    
                    {testResult.success ? (
                      <div>
                        <span className="font-medium">
                          Translated ({supportedLanguages.find(l => l.code === testResult.toLang)?.name}):
                        </span>
                        <div className="bg-muted p-2 rounded mt-1">{testResult.translatedText}</div>
                      </div>
                    ) : (
                      <div>
                        <span className="font-medium text-red-600">Error:</span>
                        <div className="bg-red-50 p-2 rounded mt-1 text-red-700">{testResult.error}</div>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          </>
        )}

        {loading && (
          <div className="flex items-center justify-center py-4">
            <Loader2 className="h-6 w-6 animate-spin" />
          </div>
        )}
      </CardContent>
    </Card>
  );
}
