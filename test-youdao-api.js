// Test script to verify Youdao translation functionality
const BASE_URL = 'http://localhost:3001';

async function testYoudaoAPI() {
  try {
    console.log('🧪 Testing Youdao Translation API...\n');

    // Test 1: Get supported languages
    console.log('1. Testing supported languages endpoint...');
    const languagesResponse = await fetch(`${BASE_URL}/api/youdao-translation/languages`);
    
    if (languagesResponse.ok) {
      const languages = await languagesResponse.json();
      console.log(`   ✅ Found ${languages.length} supported languages:`);
      languages.forEach(lang => {
        console.log(`      - ${lang.name} (${lang.code})`);
      });
    } else {
      console.log('   ❌ Failed to get supported languages');
      return;
    }

    // Test 2: Test translation endpoint
    console.log('\n2. Testing translation endpoint...');
    const testResponse = await fetch(`${BASE_URL}/api/youdao-translation/test`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        text: 'Welcome to our premium IPTV service',
        fromLang: 'en',
        toLang: 'fr'
      })
    });

    if (testResponse.ok) {
      const result = await testResponse.json();
      console.log('   ✅ Translation test successful!');
      console.log(`   Original: ${result.originalText}`);
      console.log(`   Translated: ${result.translatedText}`);
    } else {
      const error = await testResponse.json();
      console.log(`   ❌ Translation test failed: ${error.error}`);
    }

    // Test 3: Test checkout page translation status
    console.log('\n3. Testing checkout page translation status...');
    const statusResponse = await fetch(`${BASE_URL}/api/youdao-translation/checkout-page/1/status`);
    
    if (statusResponse.ok) {
      const status = await statusResponse.json();
      console.log('   ✅ Translation status retrieved:');
      console.log(`   Page: ${status.title}`);
      console.log(`   Translation enabled: ${status.youdaoTranslationEnabled}`);
    } else {
      const error = await statusResponse.json();
      console.log(`   ❌ Failed to get translation status: ${error.error}`);
    }

    // Test 4: Enable translation for checkout page
    console.log('\n4. Testing enable translation...');
    const enableResponse = await fetch(`${BASE_URL}/api/youdao-translation/checkout-page/1/toggle`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ enabled: true })
    });

    if (enableResponse.ok) {
      const result = await enableResponse.json();
      console.log('   ✅ Translation enabled successfully!');
      console.log(`   Message: ${result.message}`);
    } else {
      const error = await enableResponse.json();
      console.log(`   ❌ Failed to enable translation: ${error.error}`);
    }

    // Test 5: Test checkout page translation
    console.log('\n5. Testing checkout page translation...');
    const translateResponse = await fetch(`${BASE_URL}/api/youdao-translation/checkout-page/1`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        fromLang: 'en',
        toLang: 'fr'
      })
    });

    if (translateResponse.ok) {
      const result = await translateResponse.json();
      console.log('   ✅ Checkout page translation successful!');
      console.log('   Original content:');
      console.log(`     Title: ${result.originalContent.title}`);
      console.log(`     Product: ${result.originalContent.productName}`);
      console.log('   Translated content:');
      console.log(`     Title: ${result.translatedContent.title}`);
      console.log(`     Product: ${result.translatedContent.productName}`);
    } else {
      const error = await translateResponse.json();
      console.log(`   ❌ Checkout page translation failed: ${error.error}`);
    }

    // Test 6: Test public checkout page with language parameter
    console.log('\n6. Testing public checkout page with French language...');
    const publicResponse = await fetch(`${BASE_URL}/api/custom-checkout/public/trial?lang=fr`);
    
    if (publicResponse.ok) {
      const publicData = await publicResponse.json();
      console.log('   ✅ Public checkout page with French translation:');
      console.log(`   Title: ${publicData.title}`);
      console.log(`   Product Name: ${publicData.productName}`);
      console.log(`   Current Language: ${publicData.currentLanguage}`);
      console.log(`   Translation Enabled: ${publicData.youdaoTranslationEnabled}`);
    } else {
      console.log('   ❌ Failed to retrieve public checkout page');
    }

    console.log('\n🎉 Youdao Translation API testing completed!');
    console.log('\n📝 Next steps:');
    console.log('   1. Open the admin panel and edit a checkout page');
    console.log('   2. Click on the "Translations" tab');
    console.log('   3. Enable Youdao translation and test different languages');
    console.log('   4. Test the public checkout pages with ?lang=fr, ?lang=es, etc.');

  } catch (error) {
    console.error('❌ Error testing Youdao API:', error.message);
  }
}

// Run the test
testYoudaoAPI();
