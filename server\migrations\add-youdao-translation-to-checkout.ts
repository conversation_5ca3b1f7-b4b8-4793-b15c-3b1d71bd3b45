import { db } from '../db';
import { sql } from 'drizzle-orm';

export async function addYoudaoTranslationToCheckout() {
  console.log('Adding youdao_translation_enabled column to custom_checkout_pages...');

  try {
    // Add youdao_translation_enabled column
    await db.execute(sql`
      ALTER TABLE custom_checkout_pages
      ADD COLUMN youdao_translation_enabled BOOLEAN DEFAULT 0
    `);

    console.log('Successfully added youdao_translation_enabled column to custom_checkout_pages');
  } catch (error) {
    console.error('Error adding youdao_translation_enabled column to custom_checkout_pages:', error);
    // Don't throw error if column already exists
    if (!error.message?.includes('duplicate column name')) {
      throw error;
    }
  }
}
