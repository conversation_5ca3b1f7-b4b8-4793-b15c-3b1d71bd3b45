import React from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Globe } from 'lucide-react';

interface LanguageSelectorProps {
  currentLanguage: string;
  availableLanguages: string[];
  onLanguageChange: (language: string) => void;
  className?: string;
}

const LANGUAGE_NAMES = {
  en: { name: 'English', flag: '🇺🇸' },
  fr: { name: 'Français', flag: '🇫🇷' },
  es: { name: 'Español', flag: '🇪🇸' },
  pt: { name: 'Português', flag: '🇵🇹' },
  de: { name: '<PERSON><PERSON><PERSON>', flag: '🇩🇪' },
  nl: { name: 'Nederlands', flag: '🇳🇱' },
  pl: { name: '<PERSON><PERSON>', flag: '🇵🇱' },
  it: { name: 'Italiano', flag: '🇮🇹' },
  sv: { name: 'Svenska', flag: '🇸🇪' },
  fi: { name: '<PERSON><PERSON>', flag: '🇫🇮' },
  da: { name: 'Dan<PERSON>', flag: '🇩🇰' },
  ro: { name: 'Rom<PERSON><PERSON>', flag: '🇷🇴' },
  sq: { name: '<PERSON>hqi<PERSON>', flag: '🇦🇱' },
  sk: { name: '<PERSON><PERSON><PERSON><PERSON><PERSON>', flag: '🇸🇰' },
  no: { name: 'Norsk', flag: '🇳🇴' },
};

export function LanguageSelector({ 
  currentLanguage, 
  availableLanguages, 
  onLanguageChange, 
  className = "" 
}: LanguageSelectorProps) {
  const getCurrentLanguageDisplay = () => {
    const lang = LANGUAGE_NAMES[currentLanguage as keyof typeof LANGUAGE_NAMES];
    return lang ? `${lang.flag} ${lang.name}` : currentLanguage;
  };

  const getLanguageDisplay = (langCode: string) => {
    const lang = LANGUAGE_NAMES[langCode as keyof typeof LANGUAGE_NAMES];
    return lang ? `${lang.flag} ${lang.name}` : langCode;
  };

  // If only one language is available (English), don't show the selector
  if (availableLanguages.length <= 1) {
    return null;
  }

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <Globe className="h-4 w-4 text-muted-foreground" />
      <Select value={currentLanguage} onValueChange={onLanguageChange}>
        <SelectTrigger className="w-auto min-w-[140px] h-8 text-sm">
          <SelectValue>
            {getCurrentLanguageDisplay()}
          </SelectValue>
        </SelectTrigger>
        <SelectContent>
          {availableLanguages.map((langCode) => (
            <SelectItem key={langCode} value={langCode}>
              {getLanguageDisplay(langCode)}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
}
