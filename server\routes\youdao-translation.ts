import { Router } from 'express';
import { z } from 'zod';
import { db } from '../db';
import { customCheckoutPages } from '../../shared/schema';
import { eq } from 'drizzle-orm';
import {
  translateCheckoutPage,
  getSupportedLanguages,
  isSupportedLanguage,
  type SupportedLanguage,
  type CheckoutPageContent
} from '../services/youdao-translation';
import { isAdmin } from '../middleware/auth';

const router = Router();

// Schema for translation request
const translateRequestSchema = z.object({
  fromLang: z.string().optional().default('en'),
  toLang: z.string(),
});

// Get supported languages
router.get('/languages', (req, res) => {
  try {
    const languages = getSupportedLanguages();
    res.json(languages);
  } catch (error) {
    console.error('Error getting supported languages:', error);
    res.status(500).json({ error: 'Failed to get supported languages' });
  }
});

// Translate checkout page content
router.post('/checkout-page/:pageId', isAdmin, async (req, res) => {
  try {
    const pageId = parseInt(req.params.pageId);
    const { fromLang, toLang } = translateRequestSchema.parse(req.body);

    if (!isSupportedLanguage(fromLang)) {
      return res.status(400).json({ error: `Unsupported source language: ${fromLang}` });
    }

    if (!isSupportedLanguage(toLang)) {
      return res.status(400).json({ error: `Unsupported target language: ${toLang}` });
    }

    // Get the checkout page
    const [checkoutPage] = await db
      .select()
      .from(customCheckoutPages)
      .where(eq(customCheckoutPages.id, pageId))
      .limit(1);

    if (!checkoutPage) {
      return res.status(404).json({ error: 'Checkout page not found' });
    }

    // Check if Youdao translation is enabled for this page
    if (!checkoutPage.youdaoTranslationEnabled) {
      return res.status(400).json({
        error: 'Youdao translation is not enabled for this checkout page'
      });
    }

    // Prepare content for translation
    const content: CheckoutPageContent = {
      title: checkoutPage.title,
      productName: checkoutPage.productName,
      productDescription: checkoutPage.productDescription,
      confirmationMessage: checkoutPage.confirmationMessage || undefined,
      headerTitle: checkoutPage.headerTitle || undefined,
      footerText: checkoutPage.footerText || undefined
    };

    // Translate the content
    const translatedContent = await translateCheckoutPage(
      content,
      fromLang as SupportedLanguage,
      toLang as SupportedLanguage
    );

    res.json({
      success: true,
      originalContent: content,
      translatedContent,
      fromLang,
      toLang
    });

  } catch (error) {
    console.error('Error translating checkout page:', error);
    res.status(500).json({ error: 'Failed to translate checkout page' });
  }
});

// Enable/disable Youdao translation for a checkout page
router.patch('/checkout-page/:pageId/toggle', isAdmin, async (req, res) => {
  try {
    const pageId = parseInt(req.params.pageId);
    const { enabled } = z.object({ enabled: z.boolean() }).parse(req.body);

    // Update the checkout page
    const [updatedPage] = await db
      .update(customCheckoutPages)
      .set({
        youdaoTranslationEnabled: enabled ? 1 : 0,
        updatedAt: new Date().toISOString()
      })
      .where(eq(customCheckoutPages.id, pageId))
      .returning();

    if (!updatedPage) {
      return res.status(404).json({ error: 'Checkout page not found' });
    }

    res.json({
      success: true,
      pageId,
      youdaoTranslationEnabled: Boolean(updatedPage.youdaoTranslationEnabled),
      message: `Youdao translation ${enabled ? 'enabled' : 'disabled'} for checkout page`
    });

  } catch (error) {
    console.error('Error toggling Youdao translation:', error);
    res.status(500).json({ error: 'Failed to toggle Youdao translation' });
  }
});

// Get Youdao translation status for a checkout page
router.get('/checkout-page/:pageId/status', isAdmin, async (req, res) => {
  try {
    const pageId = parseInt(req.params.pageId);

    // Get the checkout page
    const [checkoutPage] = await db
      .select({
        id: customCheckoutPages.id,
        title: customCheckoutPages.title,
        youdaoTranslationEnabled: customCheckoutPages.youdaoTranslationEnabled
      })
      .from(customCheckoutPages)
      .where(eq(customCheckoutPages.id, pageId))
      .limit(1);

    if (!checkoutPage) {
      return res.status(404).json({ error: 'Checkout page not found' });
    }

    res.json({
      pageId: checkoutPage.id,
      title: checkoutPage.title,
      youdaoTranslationEnabled: Boolean(checkoutPage.youdaoTranslationEnabled),
      supportedLanguages: getSupportedLanguages()
    });

  } catch (error) {
    console.error('Error getting Youdao translation status:', error);
    res.status(500).json({ error: 'Failed to get Youdao translation status' });
  }
});

// Test translation endpoint (for debugging)
router.post('/test', isAdmin, async (req, res) => {
  try {
    const { text, fromLang = 'en', toLang } = z.object({
      text: z.string(),
      fromLang: z.string().optional(),
      toLang: z.string()
    }).parse(req.body);

    if (!isSupportedLanguage(fromLang)) {
      return res.status(400).json({ error: `Unsupported source language: ${fromLang}` });
    }

    if (!isSupportedLanguage(toLang)) {
      return res.status(400).json({ error: `Unsupported target language: ${toLang}` });
    }

    const { translateText } = await import('../services/youdao-translation');
    const translatedText = await translateText(
      text,
      fromLang as SupportedLanguage,
      toLang as SupportedLanguage
    );

    res.json({
      success: true,
      originalText: text,
      translatedText,
      fromLang,
      toLang
    });

  } catch (error) {
    console.error('Error testing translation:', error);
    res.status(500).json({ error: 'Failed to test translation' });
  }
});

export default router;
