// Test script to verify translation functionality
const fetch = require('node-fetch');

const BASE_URL = 'http://localhost:3001';
const CHECKOUT_PAGE_ID = 3; // The "smartonn" checkout page

// Test data for French translation
const frenchTranslation = {
  languageCode: 'fr',
  title: 'smartonn (Français)',
  productName: 'cadeaux',
  productDescription: 'Description en français pour les cadeaux',
  confirmationMessage: '<div class="space-y-3"><p><strong>🛒 Prêt à finaliser votre achat ?</strong></p><p>✅ <strong>Ce que vous obtenez :</strong></p><ul class="list-disc list-inside space-y-1"><li>Accès instantané à votre produit</li><li>Support client complet</li><li>Traitement de paiement sécurisé</li></ul><p class="text-sm text-muted-foreground mt-3">💳 <em>Votre paiement sera traité en toute sécurité</em></p><p class="text-xs text-muted-foreground">En continuant, vous acceptez nos conditions de service et notre politique de confidentialité.</p></div>',
  headerTitle: 'Mon Magasin',
  footerText: '© 2024 Mon Magasin. Tous droits réservés.'
};

async function testTranslations() {
  try {
    console.log('🧪 Testing Translation API...\n');

    // Test 1: Get existing translations (should be empty initially)
    console.log('1. Getting existing translations...');
    const getResponse = await fetch(`${BASE_URL}/api/custom-checkout-translations/${CHECKOUT_PAGE_ID}`);
    
    if (getResponse.status === 401) {
      console.log('❌ Authentication required. Please log in to the admin panel first.');
      return;
    }
    
    const existingTranslations = await getResponse.json();
    console.log(`   Found ${existingTranslations.length} existing translations`);

    // Test 2: Create a French translation
    console.log('\n2. Creating French translation...');
    const createResponse = await fetch(`${BASE_URL}/api/custom-checkout-translations/${CHECKOUT_PAGE_ID}/fr`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(frenchTranslation)
    });

    if (createResponse.ok) {
      const createdTranslation = await createResponse.json();
      console.log('   ✅ French translation created successfully!');
      console.log(`   Translation ID: ${createdTranslation.id}`);
    } else {
      const error = await createResponse.json();
      console.log(`   ❌ Failed to create translation: ${error.message}`);
      return;
    }

    // Test 3: Get the specific French translation
    console.log('\n3. Retrieving French translation...');
    const getFrResponse = await fetch(`${BASE_URL}/api/custom-checkout-translations/${CHECKOUT_PAGE_ID}/fr`);
    
    if (getFrResponse.ok) {
      const frTranslation = await getFrResponse.json();
      console.log('   ✅ French translation retrieved successfully!');
      console.log(`   Product Name: ${frTranslation.productName}`);
    } else {
      console.log('   ❌ Failed to retrieve French translation');
    }

    // Test 4: Test the public checkout page with language parameter
    console.log('\n4. Testing public checkout page with French language...');
    const publicResponse = await fetch(`${BASE_URL}/api/custom-checkout/public/smartonn?lang=fr`);
    
    if (publicResponse.ok) {
      const publicData = await publicResponse.json();
      console.log('   ✅ Public checkout page with French translation:');
      console.log(`   Product Name: ${publicData.productName}`);
      console.log(`   Current Language: ${publicData.currentLanguage}`);
      
      // Check if translation was applied
      if (publicData.productName === 'cadeaux') {
        console.log('   🎉 Translation is working correctly!');
      } else {
        console.log('   ⚠️  Translation may not be applied correctly');
      }
    } else {
      console.log('   ❌ Failed to retrieve public checkout page');
    }

    // Test 5: Test URL structure for different languages
    console.log('\n5. Testing language-specific URLs...');
    console.log(`   English: ${BASE_URL}/checkout/smartonn`);
    console.log(`   French:  ${BASE_URL}/fr/checkout/smartonn`);
    console.log(`   Spanish: ${BASE_URL}/es/checkout/smartonn`);
    console.log(`   German:  ${BASE_URL}/de/checkout/smartonn`);

    console.log('\n🎉 Translation API testing completed!');
    console.log('\n📝 Next steps:');
    console.log('   1. Open the admin panel and edit the "smartonn" checkout page');
    console.log('   2. Click on the "Translations" tab');
    console.log('   3. You should see the French translation listed');
    console.log('   4. Try adding more translations for other languages');
    console.log(`   5. Test the French checkout page at: ${BASE_URL}/fr/checkout/smartonn`);

  } catch (error) {
    console.error('❌ Error testing translations:', error.message);
  }
}

// Run the test
testTranslations();
