import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { Trash2, Plus, Globe, Copy } from 'lucide-react';

// Supported languages
const SUPPORTED_LANGUAGES = [
  { code: 'fr', name: 'French (Français)', flag: '🇫🇷' },
  { code: 'es', name: 'Spanish (Español)', flag: '🇪🇸' },
  { code: 'de', name: 'German (Deutsch)', flag: '🇩🇪' },
  { code: 'it', name: 'Italian (Italiano)', flag: '🇮🇹' },
  { code: 'pt', name: 'Portuguese (Português)', flag: '🇵🇹' },
  { code: 'nl', name: 'Dutch (Nederlands)', flag: '🇳🇱' },
  { code: 'ru', name: 'Russian (Русский)', flag: '🇷🇺' },
  { code: 'ar', name: 'Arabic (العربية)', flag: '🇸🇦' },
  { code: 'zh', name: 'Chinese (中文)', flag: '🇨🇳' },
  { code: 'ja', name: 'Japanese (日本語)', flag: '🇯🇵' },
];

// Translation form schema
const translationSchema = z.object({
  languageCode: z.string().min(2, 'Language code is required'),
  title: z.string().optional(),
  productName: z.string().optional(),
  productDescription: z.string().optional(),
  confirmationMessage: z.string().optional(),
  headerTitle: z.string().optional(),
  footerText: z.string().optional(),
});

type TranslationFormValues = z.infer<typeof translationSchema>;

interface CheckoutTranslationsProps {
  checkoutPageId: number;
  checkoutPageSlug: string;
  originalData: {
    title: string;
    productName: string;
    productDescription: string;
    confirmationMessage?: string;
    headerTitle?: string;
    footerText?: string;
  };
}

export function CheckoutTranslations({ checkoutPageId, checkoutPageSlug, originalData }: CheckoutTranslationsProps) {
  const [selectedLanguage, setSelectedLanguage] = useState<string>('');
  const [activeTab, setActiveTab] = useState<string>('list');
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch existing translations
  const { data: translations = [], isLoading } = useQuery({
    queryKey: [`/api/custom-checkout-translations/${checkoutPageId}`],
    queryFn: () => apiRequest(`/api/custom-checkout-translations/${checkoutPageId}`, 'GET'),
  });

  // Form for translation
  const form = useForm<TranslationFormValues>({
    resolver: zodResolver(translationSchema),
    defaultValues: {
      languageCode: '',
      title: '',
      productName: '',
      productDescription: '',
      confirmationMessage: '',
      headerTitle: '',
      footerText: '',
    },
  });

  // Save translation mutation
  const saveTranslationMutation = useMutation({
    mutationFn: (data: TranslationFormValues) => {
      return apiRequest(
        `/api/custom-checkout-translations/${checkoutPageId}/${data.languageCode}`,
        'PUT',
        data
      );
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/custom-checkout-translations/${checkoutPageId}`] });
      toast({
        title: 'Success',
        description: 'Translation saved successfully',
      });
      setActiveTab('list');
      form.reset();
      setSelectedLanguage('');
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to save translation',
        variant: 'destructive',
      });
    },
  });

  // Delete translation mutation
  const deleteTranslationMutation = useMutation({
    mutationFn: (languageCode: string) => {
      return apiRequest(
        `/api/custom-checkout-translations/${checkoutPageId}/${languageCode}`,
        'DELETE'
      );
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/custom-checkout-translations/${checkoutPageId}`] });
      toast({
        title: 'Success',
        description: 'Translation deleted successfully',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to delete translation',
        variant: 'destructive',
      });
    },
  });

  // Load existing translation when editing
  const loadTranslation = (languageCode: string) => {
    const translation = translations.find((t: any) => t.languageCode === languageCode);
    if (translation) {
      form.reset({
        languageCode: translation.languageCode,
        title: translation.title || '',
        productName: translation.productName || '',
        productDescription: translation.productDescription || '',
        confirmationMessage: translation.confirmationMessage || '',
        headerTitle: translation.headerTitle || '',
        footerText: translation.footerText || '',
      });
    } else {
      form.reset({
        languageCode,
        title: '',
        productName: '',
        productDescription: '',
        confirmationMessage: '',
        headerTitle: '',
        footerText: '',
      });
    }
    setSelectedLanguage(languageCode);
    setActiveTab('edit');
  };

  // Get available languages (not yet translated)
  const availableLanguages = SUPPORTED_LANGUAGES.filter(
    lang => !translations.some((t: any) => t.languageCode === lang.code)
  );

  // Copy URL with language
  const copyLanguageUrl = (languageCode: string) => {
    const url = `${window.location.origin}/${languageCode}/checkout/${checkoutPageSlug}`;
    navigator.clipboard.writeText(url);
    toast({
      title: 'Copied!',
      description: `URL copied: ${url}`,
    });
  };

  const onSubmit = (values: TranslationFormValues) => {
    saveTranslationMutation.mutate(values);
  };

  return (
    <div className="space-y-6">
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="list">
            <Globe className="mr-2 h-4 w-4" />
            Translations ({translations.length})
          </TabsTrigger>
          <TabsTrigger value="edit">
            <Plus className="mr-2 h-4 w-4" />
            Add/Edit Translation
          </TabsTrigger>
        </TabsList>

        <TabsContent value="list" className="space-y-4">
          {isLoading ? (
            <div>Loading translations...</div>
          ) : (
            <>
              {translations.length === 0 ? (
                <Card>
                  <CardContent className="pt-6">
                    <div className="text-center text-muted-foreground">
                      <Globe className="mx-auto h-12 w-12 mb-4 opacity-50" />
                      <p>No translations yet. Add your first translation to make this checkout page available in multiple languages.</p>
                    </div>
                  </CardContent>
                </Card>
              ) : (
                <div className="grid gap-4">
                  {translations.map((translation: any) => {
                    const language = SUPPORTED_LANGUAGES.find(l => l.code === translation.languageCode);
                    return (
                      <Card key={translation.languageCode}>
                        <CardContent className="pt-6">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-3">
                              <span className="text-2xl">{language?.flag}</span>
                              <div>
                                <h3 className="font-medium">{language?.name || translation.languageCode}</h3>
                                <p className="text-sm text-muted-foreground">
                                  {translation.title || originalData.title}
                                </p>
                              </div>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => copyLanguageUrl(translation.languageCode)}
                              >
                                <Copy className="h-4 w-4 mr-2" />
                                Copy URL
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => loadTranslation(translation.languageCode)}
                              >
                                Edit
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => deleteTranslationMutation.mutate(translation.languageCode)}
                                disabled={deleteTranslationMutation.isPending}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    );
                  })}
                </div>
              )}

              {availableLanguages.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Add New Translation</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
                      {availableLanguages.map((language) => (
                        <Button
                          key={language.code}
                          variant="outline"
                          className="justify-start"
                          onClick={() => loadTranslation(language.code)}
                        >
                          <span className="mr-2">{language.flag}</span>
                          {language.name.split(' ')[0]}
                        </Button>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}
            </>
          )}
        </TabsContent>

        <TabsContent value="edit" className="space-y-4">
          {selectedLanguage ? (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <span className="mr-2">
                    {SUPPORTED_LANGUAGES.find(l => l.code === selectedLanguage)?.flag}
                  </span>
                  Translate to {SUPPORTED_LANGUAGES.find(l => l.code === selectedLanguage)?.name}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                  <input type="hidden" {...form.register('languageCode')} value={selectedLanguage} />
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <h3 className="font-medium text-muted-foreground">Original Content</h3>
                      
                      <div>
                        <Label>Title</Label>
                        <div className="mt-1 p-3 bg-muted rounded-md text-sm">
                          {originalData.title}
                        </div>
                      </div>

                      <div>
                        <Label>Product Name</Label>
                        <div className="mt-1 p-3 bg-muted rounded-md text-sm">
                          {originalData.productName}
                        </div>
                      </div>

                      <div>
                        <Label>Product Description</Label>
                        <div className="mt-1 p-3 bg-muted rounded-md text-sm max-h-32 overflow-y-auto">
                          <div dangerouslySetInnerHTML={{ __html: originalData.productDescription }} />
                        </div>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <h3 className="font-medium text-muted-foreground">Translation</h3>
                      
                      <div>
                        <Label htmlFor="title">Title</Label>
                        <Input
                          id="title"
                          {...form.register('title')}
                          placeholder="Translated title..."
                        />
                      </div>

                      <div>
                        <Label htmlFor="productName">Product Name</Label>
                        <Input
                          id="productName"
                          {...form.register('productName')}
                          placeholder="Translated product name..."
                        />
                      </div>

                      <div>
                        <Label htmlFor="productDescription">Product Description</Label>
                        <Textarea
                          id="productDescription"
                          {...form.register('productDescription')}
                          placeholder="Translated product description..."
                          rows={4}
                        />
                      </div>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <div>
                        <Label>Header Title (Original)</Label>
                        <div className="mt-1 p-3 bg-muted rounded-md text-sm">
                          {originalData.headerTitle || 'Not set'}
                        </div>
                      </div>

                      <div>
                        <Label>Footer Text (Original)</Label>
                        <div className="mt-1 p-3 bg-muted rounded-md text-sm">
                          {originalData.footerText || 'Not set'}
                        </div>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <div>
                        <Label htmlFor="headerTitle">Header Title</Label>
                        <Input
                          id="headerTitle"
                          {...form.register('headerTitle')}
                          placeholder="Translated header title..."
                        />
                      </div>

                      <div>
                        <Label htmlFor="footerText">Footer Text</Label>
                        <Input
                          id="footerText"
                          {...form.register('footerText')}
                          placeholder="Translated footer text..."
                        />
                      </div>
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="confirmationMessage">Confirmation Message</Label>
                    <Textarea
                      id="confirmationMessage"
                      {...form.register('confirmationMessage')}
                      placeholder="Translated confirmation message (HTML allowed)..."
                      rows={4}
                    />
                    <p className="text-xs text-muted-foreground mt-1">
                      Original: {originalData.confirmationMessage ? 
                        <span dangerouslySetInnerHTML={{ __html: originalData.confirmationMessage.substring(0, 100) + '...' }} /> : 
                        'Not set'
                      }
                    </p>
                  </div>

                  <div className="flex justify-end space-x-2">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => {
                        setActiveTab('list');
                        setSelectedLanguage('');
                        form.reset();
                      }}
                    >
                      Cancel
                    </Button>
                    <Button
                      type="submit"
                      disabled={saveTranslationMutation.isPending}
                    >
                      {saveTranslationMutation.isPending ? 'Saving...' : 'Save Translation'}
                    </Button>
                  </div>
                </form>
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardContent className="pt-6">
                <div className="text-center text-muted-foreground">
                  <p>Select a language from the list to start translating.</p>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
