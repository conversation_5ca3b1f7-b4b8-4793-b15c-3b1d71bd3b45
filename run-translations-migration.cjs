const Database = require('better-sqlite3');
const path = require('path');

// Initialize SQLite database
const dbPath = path.join(__dirname, 'data.db');
console.log('Database path:', dbPath);

async function runMigration() {
  try {
    console.log('🚀 Starting translations migration...');
    
    const db = new Database(dbPath);
    
    // Check if table already exists
    const tableInfo = db.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name='custom_checkout_page_translations'").get();
    
    if (tableInfo) {
      console.log('Table custom_checkout_page_translations already exists');
      db.close();
      return;
    }
    
    // Create the custom_checkout_page_translations table
    db.exec(`
      CREATE TABLE IF NOT EXISTS custom_checkout_page_translations (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        checkout_page_id INTEGER NOT NULL,
        language_code TEXT NOT NULL,
        title TEXT,
        product_name TEXT,
        product_description TEXT,
        confirmation_message TEXT,
        header_title TEXT,
        footer_text TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (checkout_page_id) REFERENCES custom_checkout_pages(id) ON DELETE CASCADE,
        UNIQUE(checkout_page_id, language_code)
      )
    `);

    // Create indexes for faster lookups
    db.exec(`
      CREATE INDEX IF NOT EXISTS idx_checkout_translations_page_id 
      ON custom_checkout_page_translations(checkout_page_id)
    `);

    db.exec(`
      CREATE INDEX IF NOT EXISTS idx_checkout_translations_language 
      ON custom_checkout_page_translations(language_code)
    `);

    db.close();
    console.log('✅ Translations migration completed successfully!');
    process.exit(0);
  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  }
}

runMigration();
