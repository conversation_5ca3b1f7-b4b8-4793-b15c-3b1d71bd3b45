// Simple script to run the translation table migration
import { addCheckoutPageTranslations } from './server/migrations/add-checkout-page-translations.ts';

async function runMigration() {
  try {
    console.log('Running checkout page translations migration...');
    await addCheckoutPageTranslations();
    console.log('Migration completed successfully!');
    process.exit(0);
  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  }
}

runMigration();
