const Database = require('better-sqlite3');
const path = require('path');

// Initialize SQLite database
const dbPath = path.join(__dirname, 'data.db');
console.log('Database path:', dbPath);

try {
  const db = new Database(dbPath);
  
  console.log('Adding youdao_translation_enabled column to custom_checkout_pages...');
  
  // Check if column already exists
  const tableInfo = db.prepare("PRAGMA table_info(custom_checkout_pages)").all();
  const columnExists = tableInfo.some(col => col.name === 'youdao_translation_enabled');
  
  if (columnExists) {
    console.log('Column youdao_translation_enabled already exists');
  } else {
    // Add youdao_translation_enabled column
    db.exec(`ALTER TABLE custom_checkout_pages ADD COLUMN youdao_translation_enabled INTEGER DEFAULT 0`);
    console.log('Successfully added youdao_translation_enabled column to custom_checkout_pages');
  }
  
  db.close();
  console.log('✅ Youdao translation migration completed successfully');
} catch (error) {
  console.error('❌ Youdao translation migration failed:', error);
  process.exit(1);
}
