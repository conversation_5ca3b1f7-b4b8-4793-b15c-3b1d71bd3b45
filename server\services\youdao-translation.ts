import crypto from 'crypto';

// Youdao API configuration
const YOUDAO_CONFIG = {
  appKey: '31c04e3692cbbf41',
  appSecret: 'W8HapFIdpZLnmHlpLpxPy4age2JwSQEK',
  apiUrl: 'https://openapi.youdao.com/api'
};

// Language mappings for Youdao API
export const YOUDAO_LANGUAGE_CODES = {
  'en': 'en',      // English
  'fr': 'fr',      // French
  'es': 'es',      // Spanish
  'pt': 'pt',      // Portuguese
  'de': 'de',      // German
  'nl': 'nl',      // Dutch
  'pl': 'pl',      // Polish
  'it': 'it',      // Italian
  'sv': 'sv',      // Swedish
  'fi': 'fi',      // Finnish
  'da': 'da',      // Danish
  'ro': 'ro',      // Romanian
  'sq': 'sq',      // Albanian
  'sk': 'sk',      // Slovak
  'no': 'no',      // Norwegian
} as const;

export type SupportedLanguage = keyof typeof YOUDAO_LANGUAGE_CODES;

// Language display names
export const LANGUAGE_NAMES = {
  'en': 'English',
  'fr': 'French',
  'es': 'Spanish',
  'pt': 'Portuguese',
  'de': 'German',
  'nl': 'Dutch',
  'pl': 'Polish',
  'it': 'Italian',
  'sv': 'Swedish',
  'fi': 'Finnish',
  'da': 'Danish',
  'ro': 'Romanian',
  'sq': 'Albanian',
  'sk': 'Slovak',
  'no': 'Norwegian',
} as const;

interface YoudaoTranslationRequest {
  q: string;
  from: string;
  to: string;
  appKey: string;
  salt: string;
  sign: string;
  signType: string;
  curtime: string;
}

interface YoudaoTranslationResponse {
  errorCode: string;
  query?: string;
  translation?: string[];
  l?: string;
  dict?: {
    url: string;
  };
  webdict?: {
    url: string;
  };
  tSpeakUrl?: string;
  speakUrl?: string;
}

/**
 * Generate signature for Youdao API
 */
function generateSign(appKey: string, query: string, salt: string, curtime: string, appSecret: string): string {
  // Calculate input according to Youdao documentation
  let input: string;
  if (query.length <= 20) {
    input = query;
  } else {
    input = query.substring(0, 10) + query.length + query.substring(query.length - 10);
  }

  // Create signature: sha256(appKey + input + salt + curtime + appSecret)
  const signStr = appKey + input + salt + curtime + appSecret;
  return crypto.createHash('sha256').update(signStr).digest('hex');
}

/**
 * Translate text using Youdao API
 */
export async function translateText(
  text: string,
  fromLang: SupportedLanguage = 'en',
  toLang: SupportedLanguage
): Promise<string> {
  if (!text || text.trim() === '') {
    return text;
  }

  // Don't translate if source and target languages are the same
  if (fromLang === toLang) {
    return text;
  }

  try {
    const salt = Date.now().toString();
    const curtime = Math.round(Date.now() / 1000).toString();
    
    const sign = generateSign(
      YOUDAO_CONFIG.appKey,
      text,
      salt,
      curtime,
      YOUDAO_CONFIG.appSecret
    );

    const requestData: YoudaoTranslationRequest = {
      q: text,
      from: YOUDAO_LANGUAGE_CODES[fromLang],
      to: YOUDAO_LANGUAGE_CODES[toLang],
      appKey: YOUDAO_CONFIG.appKey,
      salt,
      sign,
      signType: 'v3',
      curtime
    };

    // Convert to URL-encoded form data
    const formData = new URLSearchParams();
    Object.entries(requestData).forEach(([key, value]) => {
      formData.append(key, value);
    });

    console.log(`Translating from ${fromLang} to ${toLang}: "${text.substring(0, 50)}..."`);

    const response = await fetch(YOUDAO_CONFIG.apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: formData.toString()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result: YoudaoTranslationResponse = await response.json();

    if (result.errorCode !== '0') {
      console.error('Youdao API error:', result.errorCode);
      throw new Error(`Youdao API error: ${result.errorCode}`);
    }

    if (!result.translation || result.translation.length === 0) {
      console.warn('No translation returned from Youdao API');
      return text; // Return original text if no translation
    }

    const translatedText = result.translation[0];
    console.log(`Translation successful: "${translatedText.substring(0, 50)}..."`);
    
    return translatedText;

  } catch (error) {
    console.error('Translation error:', error);
    // Return original text if translation fails
    return text;
  }
}

/**
 * Translate multiple texts in batch
 */
export async function translateTexts(
  texts: string[],
  fromLang: SupportedLanguage = 'en',
  toLang: SupportedLanguage
): Promise<string[]> {
  const results: string[] = [];
  
  // Process translations sequentially to avoid rate limiting
  for (const text of texts) {
    const translated = await translateText(text, fromLang, toLang);
    results.push(translated);
    
    // Add small delay between requests to be respectful to the API
    await new Promise(resolve => setTimeout(resolve, 100));
  }
  
  return results;
}

/**
 * Translate checkout page content
 */
export interface CheckoutPageContent {
  title: string;
  productName: string;
  productDescription: string;
  confirmationMessage?: string;
  headerTitle?: string;
  footerText?: string;
}

export async function translateCheckoutPage(
  content: CheckoutPageContent,
  fromLang: SupportedLanguage = 'en',
  toLang: SupportedLanguage
): Promise<CheckoutPageContent> {
  console.log(`Translating checkout page content from ${fromLang} to ${toLang}`);

  const [
    translatedTitle,
    translatedProductName,
    translatedProductDescription,
    translatedConfirmationMessage,
    translatedHeaderTitle,
    translatedFooterText
  ] = await translateTexts([
    content.title,
    content.productName,
    content.productDescription,
    content.confirmationMessage || '',
    content.headerTitle || '',
    content.footerText || ''
  ], fromLang, toLang);

  return {
    title: translatedTitle,
    productName: translatedProductName,
    productDescription: translatedProductDescription,
    confirmationMessage: translatedConfirmationMessage || undefined,
    headerTitle: translatedHeaderTitle || undefined,
    footerText: translatedFooterText || undefined
  };
}

/**
 * Check if a language is supported
 */
export function isSupportedLanguage(lang: string): lang is SupportedLanguage {
  return lang in YOUDAO_LANGUAGE_CODES;
}

/**
 * Get all supported languages
 */
export function getSupportedLanguages(): Array<{ code: SupportedLanguage; name: string }> {
  return Object.entries(LANGUAGE_NAMES).map(([code, name]) => ({
    code: code as SupportedLanguage,
    name
  }));
}
