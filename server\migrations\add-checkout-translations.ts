import { db } from '../db';
import { sql } from 'drizzle-orm';

export async function addCheckoutTranslations() {
  console.log('Adding custom_checkout_page_translations table...');

  try {
    // Create the custom_checkout_page_translations table
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS custom_checkout_page_translations (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        checkout_page_id INTEGER NOT NULL,
        language_code TEXT NOT NULL,
        title TEXT,
        product_name TEXT,
        product_description TEXT,
        confirmation_message TEXT,
        header_title TEXT,
        footer_text TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (checkout_page_id) REFERENCES custom_checkout_pages(id) ON DELETE CASCADE,
        UNIQUE(checkout_page_id, language_code)
      )
    `);

    // Create indexes for faster lookups
    await db.execute(sql`
      CREATE INDEX IF NOT EXISTS idx_checkout_translations_page_id 
      ON custom_checkout_page_translations(checkout_page_id)
    `);

    await db.execute(sql`
      CREATE INDEX IF NOT EXISTS idx_checkout_translations_language 
      ON custom_checkout_page_translations(language_code)
    `);

    console.log('Successfully added custom_checkout_page_translations table with indexes');
  } catch (error) {
    console.error('Error adding custom_checkout_page_translations table:', error);
    throw error;
  }
}
